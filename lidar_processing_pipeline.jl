# LIDAR Data Processing Pipeline - Main Orchestrator
# Modular system for processing LICEL LIDAR data with UInt32 photon count integrity
# Coordinates all processing stages from raw data to final analysis

using Printf

# Include all processing modules
include("modules/licel_raw_reader.jl")
include("modules/noise_correction.jl")
include("modules/range_correction.jl")
include("modules/normalization.jl")
include("modules/molecular_backscatter.jl")
include("modules/backscatter_analysis.jl")
include("modules/asr_analysis.jl")
include("modules/aod_calculation.jl")
include("modules/rtdi_generator.jl")

"""
    setup_directories()

Create necessary directory structure for the processing pipeline.
"""
function setup_directories()
    directories = ["data", "plots", "results", "modules"]
    
    for dir in directories
        if !isdir(dir)
            mkdir(dir)
            println("✅ Created directory: $dir")
        end
    end
end

"""
    process_single_lidar_file(file_path::String; 
                             output_dir::String="data",
                             plot_results::Bool=true) -> Dict{String, String}

Process a single LICEL file through the complete pipeline.
Returns dictionary with paths to all generated files.
"""
function process_single_lidar_file(file_path::String; 
                                  output_dir::String="data",
                                  plot_results::Bool=true)
    
    println("="^60)
    println("PROCESSING SINGLE LIDAR FILE")
    println("File: $file_path")
    println("="^60)
    
    if !isfile(file_path)
        error("File not found: $file_path")
    end
    
    # Setup directories
    setup_directories()
    
    # Dictionary to store all output file paths
    output_files = Dict{String, String}()
    
    try
        # Step 1: Read raw LICEL file and convert to CSV
        println("\n1️⃣  READING RAW LICEL FILE")
        println("-"^30)
        
        data = read_licel_raw_file(file_path)
        
        # Check for overflow
        overflow_analysis = check_overflow_values(data.raw_counts)
        
        # Generate CSV filename
        base_name = basename(file_path)
        csv_path = joinpath(output_dir, "$(base_name).csv")
        save_lidar_csv(data, csv_path)
        output_files["raw_csv"] = csv_path
        
        # Step 2: Noise Correction
        println("\n2️⃣  NOISE CORRECTION")
        println("-"^30)
        
        noise_corrected_csv = process_noise_correction_csv(csv_path, "", 
                                                          plot_results=plot_results, 
                                                          save_plots=true)
        output_files["noise_corrected"] = noise_corrected_csv
        
        # Step 3: Range Correction
        println("\n3️⃣  RANGE CORRECTION")
        println("-"^30)
        
        range_corrected_csv = process_range_correction_csv(noise_corrected_csv, "", 
                                                          plot_results=plot_results, 
                                                          save_plots=true)
        output_files["range_corrected"] = range_corrected_csv
        
        # Step 4: Normalization
        println("\n4️⃣  NORMALIZATION")
        println("-"^30)
        
        normalized_csv = process_normalization_csv(range_corrected_csv, "", 
                                                  plot_results=plot_results, 
                                                  save_plots=true)
        output_files["normalized"] = normalized_csv
        
        # Step 5: Molecular Backscatter
        println("\n5️⃣  MOLECULAR BACKSCATTER")
        println("-"^30)

        molecular_csv = process_molecular_backscatter_csv(normalized_csv, "",
                                                         plot_results=plot_results,
                                                         save_plots=true)
        output_files["molecular"] = molecular_csv

        # Step 6: Backscatter Analysis
        println("\n6️⃣  BACKSCATTER ANALYSIS")
        println("-"^30)

        backscatter_csv = process_backscatter_analysis_csv(molecular_csv, "",
                                                          plot_results=plot_results,
                                                          save_plots=true)
        output_files["backscatter"] = backscatter_csv

        # Step 7: ASR Analysis
        println("\n7️⃣  ASR ANALYSIS")
        println("-"^30)

        asr_csv = process_asr_analysis_csv(backscatter_csv, "",
                                          plot_results=plot_results,
                                          save_plots=true)
        output_files["asr"] = asr_csv

        # Step 8: AOD Calculation
        println("\n8️⃣  AOD CALCULATION")
        println("-"^30)

        aod_csv = process_aod_analysis_csv(backscatter_csv, "",
                                          plot_results=plot_results,
                                          save_plots=true)
        output_files["aod"] = aod_csv

        println("\n" * "="^60)
        println("✅ SINGLE FILE PROCESSING COMPLETE")
        println("Generated files:")
        for (stage, path) in output_files
            println("  $stage: $(basename(path))")
        end
        println("="^60)
        
    catch e
        println("❌ Error during processing: $e")
        rethrow(e)
    end
    
    return output_files
end

"""
    run_complete_pipeline(; 
                         input_dirs::Vector{String}=String[],
                         output_dir::String="data",
                         plot_results::Bool=true) -> Dict{String, Vector{String}}

Run the complete processing pipeline on all clear sky data.
"""
function run_complete_pipeline(; 
                              input_dirs::Vector{String}=String[],
                              output_dir::String="data",
                              plot_results::Bool=true)
    
    println("="^60)
    println("COMPLETE LIDAR PROCESSING PIPELINE")
    println("="^60)
    
    # Setup directories
    setup_directories()
    
    # Use default clear sky directories if none provided
    if isempty(input_dirs)
        input_dirs = [
            "clear sky CBL Aratrika/08May2023_CBL_clearsky",
            "clear sky CBL Aratrika/10May2023_CBL_clear Sky", 
            "clear sky CBL Aratrika/16May2023_CBL_study_clear_sky",
            "clear sky CBL Aratrika/18May2023_CBL_study_clearsky"
        ]
    end
    
    # Dictionary to store all output files by stage
    all_outputs = Dict{String, Vector{String}}()
    
    try
        # Step 1: Batch process raw LICEL files
        println("\n1️⃣  BATCH PROCESSING RAW LICEL FILES")
        println("-"^40)
        
        raw_csv_files = String[]
        for input_dir in input_dirs
            if isdir(input_dir)
                processed_files = process_clear_sky_folder(input_dir, output_dir)
                append!(raw_csv_files, processed_files)
            else
                println("⚠️  Directory not found: $input_dir")
            end
        end
        all_outputs["raw_csv"] = raw_csv_files
        
        # Step 2: Batch noise correction
        println("\n2️⃣  BATCH NOISE CORRECTION")
        println("-"^40)
        
        noise_corrected_files = batch_process_noise_correction(output_dir, output_dir, 
                                                              plot_results=plot_results)
        all_outputs["noise_corrected"] = noise_corrected_files
        
        # Step 3: Batch range correction
        println("\n3️⃣  BATCH RANGE CORRECTION")
        println("-"^40)
        
        range_corrected_files = batch_process_range_correction(output_dir, output_dir, 
                                                              plot_results=plot_results)
        all_outputs["range_corrected"] = range_corrected_files
        
        # Step 4: Batch normalization
        println("\n4️⃣  BATCH NORMALIZATION")
        println("-"^40)
        
        normalized_files = batch_process_normalization(output_dir, output_dir, 
                                                      plot_results=plot_results)
        all_outputs["normalized"] = normalized_files
        
        # Step 5: Batch molecular backscatter
        println("\n5️⃣  BATCH MOLECULAR BACKSCATTER")
        println("-"^40)

        molecular_files = batch_process_molecular_backscatter(output_dir, output_dir,
                                                             plot_results=plot_results)
        all_outputs["molecular"] = molecular_files

        # Step 6: Batch backscatter analysis
        println("\n6️⃣  BATCH BACKSCATTER ANALYSIS")
        println("-"^40)

        backscatter_files = batch_process_backscatter_analysis(output_dir, output_dir,
                                                              plot_results=plot_results)
        all_outputs["backscatter"] = backscatter_files

        # Step 7: Batch ASR analysis
        println("\n7️⃣  BATCH ASR ANALYSIS")
        println("-"^40)

        asr_files = batch_process_asr_analysis(output_dir, output_dir,
                                              plot_results=plot_results)
        all_outputs["asr"] = asr_files

        # Step 8: Batch AOD calculation
        println("\n8️⃣  BATCH AOD CALCULATION")
        println("-"^40)

        aod_files = batch_process_aod_analysis(output_dir, output_dir,
                                              plot_results=plot_results)
        all_outputs["aod"] = aod_files

        # Step 9: Generate RTDI maps
        println("\n9️⃣  RTDI MAP GENERATION")
        println("-"^40)

        rtdi_results = batch_generate_rtdi_maps(input_dirs,
                                               output_dir="plots")
        all_outputs["rtdi"] = rtdi_results

        # Summary
        println("\n" * "="^60)
        println("✅ COMPLETE PIPELINE FINISHED")
        println("Processing Summary:")
        for (stage, files) in all_outputs
            if stage == "rtdi"
                if isa(files, Dict)
                    println("  $stage: $(length(files)) days processed")
                else
                    println("  $stage: $(length(files)) files")
                end
            else
                println("  $stage: $(length(files)) files")
            end
        end
        println("\nOutput directories:")
        println("  Data: $output_dir")
        println("  Plots: plots/")
        println("="^60)
        
    catch e
        println("❌ Error during pipeline execution: $e")
        rethrow(e)
    end
    
    return all_outputs
end

"""
    demo_single_file()

Demonstrate processing of a single file (if available).
"""
function demo_single_file()
    println("🧪 SINGLE FILE DEMO")
    println("="^30)
    
    # Look for a sample file
    sample_dirs = [
        "clear sky CBL Aratrika/08May2023_CBL_clearsky",
        "processed/raw"
    ]
    
    sample_file = ""
    for dir in sample_dirs
        if isdir(dir)
            files = find_licel_files(dir)
            if !isempty(files)
                sample_file = files[1]
                break
            end
        end
    end
    
    if !isempty(sample_file)
        println("Processing sample file: $(basename(sample_file))")
        output_files = process_single_lidar_file(sample_file, plot_results=true)
        return output_files
    else
        println("⚠️  No sample files found for demo")
        println("Available directories:")
        for dir in sample_dirs
            if isdir(dir)
                println("  ✅ $dir")
            else
                println("  ❌ $dir (not found)")
            end
        end
        return nothing
    end
end

"""
    show_pipeline_help()

Display help information for the processing pipeline.
"""
function show_pipeline_help()
    println("="^60)
    println("LIDAR PROCESSING PIPELINE - HELP")
    println("="^60)
    println()
    println("🔧 MODULAR PROCESSING SYSTEM")
    println("This system processes LICEL LIDAR data through multiple stages:")
    println()
    println("📁 MODULES:")
    println("  • licel_raw_reader.jl       - Read raw LICEL files → CSV")
    println("  • noise_correction.jl       - Noise estimation & removal")
    println("  • range_correction.jl       - 1/z² range correction")
    println("  • normalization.jl          - Reference region normalization")
    println("  • molecular_backscatter.jl  - βₘ(z) calculation")
    println("  • backscatter_analysis.jl   - Total & aerosol backscatter")
    println("  • asr_analysis.jl           - Aerosol Scattering Ratio (ASR)")
    println("  • aod_calculation.jl        - Aerosol Optical Depth (AOD)")
    println("  • rtdi_generator.jl         - RTDI map generation")
    println()
    println("🚀 MAIN FUNCTIONS:")
    println("  • demo_single_file()                    - Process one file demo")
    println("  • process_single_lidar_file(path)       - Process single file")
    println("  • run_complete_pipeline()               - Process all data")
    println("  • batch_process_clear_sky_data()        - Raw data conversion only")
    println()
    println("📊 DATA FLOW:")
    println("  Raw LICEL → CSV → Noise → Range → Normalized → Molecular → Backscatter → ASR → AOD + RTDI")
    println()
    println("💾 OUTPUT:")
    println("  • data/     - CSV files for each processing stage")
    println("  • plots/    - Visualization plots")
    println("  • results/  - Analysis results")
    println()
    println("🎯 QUICK START:")
    println("  1. demo_single_file()           # Test with one file")
    println("  2. run_complete_pipeline()      # Process all data")
    println()
    println("="^60)
end

# Display help on load
show_pipeline_help()

# Export main functions
export process_single_lidar_file, run_complete_pipeline
export demo_single_file, show_pipeline_help
export setup_directories
