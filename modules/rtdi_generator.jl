# RTDI Generator Module - Simplified Version
# Matches RTI_Plotting approach exactly

using Plots
using Statistics

"""
    create_rtdi_map_from_raw_files(input_folder::String, max_range::Int=1000, max_files::Int=2701) -> Matrix{UInt32}

Create RTDI map directly from raw LICEL files using RTI_Plotting approach.
"""
function create_rtdi_map_from_raw_files(input_folder::String, max_range::Int=1000, max_files::Int=2701)
    if !isdir(input_folder)
        error("Input folder not found: $input_folder")
    end
    
    println("Creating RTDI map from: $input_folder")
    println("Max range: $max_range bins, Max files: $max_files")
    
    # Initialize RTDI matrix with UInt32
    rtdi_data = zeros(UInt32, max_range, max_files)
    
    files = readdir(input_folder)
    file_counter = 0
    
    for file in files
        if file_counter >= max_files
            break
        end
        
        input_file_path = joinpath(input_folder, file)
        
        if isfile(input_file_path)
            try
                open(input_file_path, "r") do input_file
                    # Skip 3 header lines
                    for _ in 1:3
                        readline(input_file)
                    end

                    # Read raw UInt32 data
                    raw_data = UInt32[]
                    while !eof(input_file)
                        push!(raw_data, read(input_file, UInt32))
                    end
                    
                    # Noise correction (range 1000-2000)
                    noise_start = min(max_range, 1000)
                    noise_end = min(length(raw_data), 2000)
                    
                    noise_factor = 0
                    if noise_end > noise_start
                        noise_factor = round(Int, mean(raw_data[noise_start:noise_end]))
                    end

                    # Apply corrections
                    for i in 5:min(max_range, length(raw_data))
                        # Noise correction
                        corrected = max(raw_data[i] - noise_factor, 0)
                        
                        # Range correction: range = 15 + (i-1) * 30
                        range = 15 + (i - 1) * 30
                        range_squared = UInt64(range) * UInt64(range)
                        
                        # Apply range-squared correction
                        final_value = UInt64(corrected) * range_squared
                        
                        # Handle overflow
                        if final_value > UInt32(0xffffffff)
                            final_value = UInt32(0xffffffff)
                        end
                        
                        rtdi_data[i, file_counter+1] = UInt32(final_value)
                    end
                    
                    println("Processed: $file")
                    file_counter += 1
                end
            catch e
                println("Error processing $file: $e")
            end
        end
    end
    
    println("RTDI map created: $(size(rtdi_data, 1)) × $(size(rtdi_data, 2))")
    println("Processed $file_counter files")
    
    return rtdi_data
end

"""
    save_rtdi_heatmap(rtdi_data::Matrix{UInt32}, output_path::String; title::String="RTDI Heatmap")

Save RTDI heatmap plot.
"""
function save_rtdi_heatmap(rtdi_data::Matrix{UInt32}, output_path::String; title::String="RTDI Heatmap")
    max_range, max_files = size(rtdi_data)
    
    plot_heatmap = heatmap(1:max_files, 1:max_range, rtdi_data, 
                          c=:thermal, 
                          xlabel="File Index", 
                          ylabel="Range Bin", 
                          title=title,
                          size=(1000, 600))
    
    mkpath(dirname(output_path))
    savefig(plot_heatmap, output_path)
    println("RTDI heatmap saved: $output_path")
    
    return plot_heatmap
end

"""
    batch_create_rtdi_maps(input_folder::String, output_folder::String; 
                          initial_max_range::Int=500, final_max_range::Int=1000, step::Int=50)

Batch create RTDI maps for different range settings.
"""
function batch_create_rtdi_maps(input_folder::String, output_folder::String;
                               initial_max_range::Int=500, final_max_range::Int=1000, step::Int=50)
    
    if !isdir(output_folder)
        mkpath(output_folder)
    end
    
    println("Batch creating RTDI maps...")
    println("Input: $input_folder")
    println("Output: $output_folder")
    println("Range: $initial_max_range to $final_max_range (step: $step)")
    
    for current_max_range in initial_max_range:step:final_max_range
        println("\nProcessing max_range: $current_max_range")
        
        rtdi_data = create_rtdi_map_from_raw_files(input_folder, current_max_range)
        
        figure_number = div((current_max_range - initial_max_range), step) + 50
        filename = joinpath(output_folder, "$(figure_number).png")
        
        save_rtdi_heatmap(rtdi_data, filename, 
                         title="RTDI Map - Range: $current_max_range bins")
    end
    
    println("\nBatch RTDI generation complete!")
end

# Export functions
export create_rtdi_map_from_raw_files, save_rtdi_heatmap, batch_create_rtdi_maps
