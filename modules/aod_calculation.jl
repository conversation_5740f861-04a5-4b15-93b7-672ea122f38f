# AOD Calculation Module - Simplified Version

using CSV
using DataFrames
using Printf
using Statistics

"""
    calculate_aod_profile(beta_aerosol::Vector{Float64}, altitude::Vector{Float64}, lidar_ratio::Float64=50.0) -> Vector{Float64}

Calculate Aerosol Optical Depth profile:
• AOD = total aerosol "thickness" vertically
• AOD = Σ [βₐ(z) × Δz] from z = h₁ to h₂
• Note: For extinction coefficient, multiply by lidar ratio: α = L × β
• AOD = Σ [α(z) × Δz] = Σ [L × βₐ(z) × Δz]
Improved with better numerical handling and realistic scaling.
"""
function calculate_aod_profile(beta_aerosol::Vector{Float64}, altitude::Vector{Float64}, lidar_ratio::Float64=20.0)
    aod_profile = zeros(Float64, length(beta_aerosol))

    # Check input data quality
    non_zero_aerosol = sum(beta_aerosol .> 0)
    max_aerosol = maximum(beta_aerosol)

    println("📊 AOD calculation input:")
    println("   Non-zero aerosol points: $non_zero_aerosol/$(length(beta_aerosol))")
    println("   Max aerosol backscatter: $max_aerosol")
    println("   Lidar ratio: $lidar_ratio sr")

    # AOD = Σ [L × βₐ(z) × Δz] (if lidar ratio L is known)
    # or AOD = Σ [βₐ(z) × Δz] (direct integration)
    # Limit integration to realistic atmospheric height (15 km)
    max_height = 15000.0  # meters
    total_contribution = 0.0

    for i in 2:length(beta_aerosol)
        # Skip if above maximum integration height
        if altitude[i] > max_height
            aod_profile[i] = aod_profile[i-1]  # Keep previous value
            continue
        end

        dz = altitude[i] - altitude[i-1]
        avg_beta = (beta_aerosol[i-1] + beta_aerosol[i]) / 2

        # Calculate contribution to AOD
        # Note: dz is in meters, beta in m^-1 sr^-1
        # For extinction: α = L × β (where L is lidar ratio in sr)
        # AOD contribution = α × dz = L × β × dz (dimensionless)
        if lidar_ratio > 0
            contribution = lidar_ratio * avg_beta * dz
        else
            # Direct integration of backscatter (less physical)
            contribution = avg_beta * dz
        end

        aod_profile[i] = aod_profile[i-1] + contribution
        total_contribution += contribution
    end

    # Fill remaining values above max_height with final AOD value
    max_height_idx = findfirst(alt -> alt > max_height, altitude)
    if max_height_idx !== nothing && max_height_idx > 1
        final_aod = aod_profile[max_height_idx - 1]
        for i in max_height_idx:length(altitude)
            aod_profile[i] = final_aod
        end
    end

    println("   Total AOD contribution: $total_contribution")

    return aod_profile
end

"""
    process_aod_calculation_csv(input_csv::String, output_csv::String="") -> String

Process AOD calculation for CSV file.
"""
function process_aod_calculation_csv(input_csv::String, output_csv::String="")
    if isempty(output_csv)
        output_csv = replace(input_csv, ".csv" => "_aod.csv")
    end
    
    println("Processing AOD calculation: $(basename(input_csv))")
    
    df = CSV.read(input_csv, DataFrame, comment="#")
    
    # Check required columns
    if !("Beta_aerosol" in names(df)) || !("Range_m" in names(df))
        error("Required columns not found. Run backscatter analysis first.")
    end
    
    beta_aerosol = df.Beta_aerosol
    altitude = df.Range_m
    
    aod_profile = calculate_aod_profile(beta_aerosol, altitude)

    # Add AOD data with appropriate precision
    df.AOD_profile = aod_profile
    df.AOD_total = fill(aod_profile[end], nrow(df))

    # Report results with scientific notation for small values
    total_aod = aod_profile[end]
    if total_aod > 1e-6
        println("📊 Total AOD: ", round(total_aod, digits=6))
    else
        println("📊 Total AOD: ", @sprintf("%.3e", total_aod))
    end
    
    mkpath(dirname(output_csv))
    CSV.write(output_csv, df)
    println("✅ AOD calculation data saved: $output_csv")
    
    return output_csv
end

"""
    batch_process_aod_calculation(input_dir::String, output_dir::String="data") -> Vector{String}

Batch process AOD calculation.
"""
function batch_process_aod_calculation(input_dir::String, output_dir::String="data")
    println("🔄 Batch processing AOD calculation...")
    
    csv_files = filter(file -> endswith(file, "_asr.csv"),
                      readdir(input_dir, join=true))
    
    if isempty(csv_files)
        println("⚠️  No ASR CSV files found")
        return String[]
    end
    
    processed_files = String[]
    
    for csv_file in csv_files
        try
            output_csv = joinpath(output_dir, replace(basename(csv_file), "_asr.csv" => "_aod.csv"))
            result_file = process_aod_calculation_csv(csv_file, output_csv)
            push!(processed_files, result_file)
        catch e
            println("❌ Error processing $(basename(csv_file)): $e")
        end
    end
    
    println("📊 Processed $(length(processed_files)) files")
    return processed_files
end

# Export functions
export calculate_aod_profile
export process_aod_calculation_csv, batch_process_aod_calculation
