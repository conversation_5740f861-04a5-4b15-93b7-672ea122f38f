# Noise Correction Module - Simplified Version

using CSV
using DataFrames
using Statistics

"""
    estimate_noise_level(photon_counts::Vector{Float64}, altitude::Vector{Float64}) -> Float64

Estimate noise level from high-altitude data (36 km–60 km) where no aerosols are present.
Use average of raw signal S_raw(z) in this range as per specification.
"""
function estimate_noise_level(photon_counts::Vector{Float64}, altitude::Vector{Float64})
    # Find indices for noise region (36-60 km) as specified
    noise_indices = Int[]
    for i in 1:length(altitude)
        if 36000.0 <= altitude[i] <= 60000.0
            push!(noise_indices, i)
        end
    end

    if isempty(noise_indices)
        println("⚠️  No data in noise region (36-60 km), using last 10% of data")
        start_idx = max(1, round(Int, 0.9 * length(photon_counts)))
        noise_indices = collect(start_idx:length(photon_counts))
    end

    # Noise = average of raw signal S_raw(z) in this range
    noise_level = mean(photon_counts[noise_indices])
    println("📊 Noise level estimated: ", round(noise_level, digits=2), " (from ", length(noise_indices), " points)")
    println("📊 Noise region: $(round(altitude[noise_indices[1]], digits=1)) - $(round(altitude[noise_indices[end]], digits=1)) m")

    return noise_level
end

"""
    apply_noise_correction(photon_counts::Vector{Float64}, noise_level::Float64) -> Vector{Float64}

Apply noise correction: S_clean(z) = S_raw(z) - Noise
Subtract noise from all bins as per specification.
"""
function apply_noise_correction(photon_counts::Vector{Float64}, noise_level::Float64)
    # S_clean(z) = S_raw(z) - Noise
    corrected_counts = photon_counts .- noise_level
    corrected_counts = max.(corrected_counts, 0.0)  # Ensure non-negative

    return corrected_counts
end

"""
    process_noise_correction_csv(input_csv::String, output_csv::String="";
                                plot_results::Bool=false, save_plots::Bool=false) -> String

Process noise correction for CSV file.
"""
function process_noise_correction_csv(input_csv::String, output_csv::String="";
                                     plot_results::Bool=false, save_plots::Bool=false)
    if isempty(output_csv)
        output_csv = replace(input_csv, ".csv" => "_noise_corrected.csv")
    end
    
    println("Processing noise correction: $(basename(input_csv))")
    
    df = CSV.read(input_csv, DataFrame, comment="#")
    
    # Check required columns
    if !("Counts" in names(df)) || !("Range_m" in names(df))
        error("Required columns 'Counts' or 'Range_m' not found")
    end
    
    photon_counts = Float64.(df.Counts)
    altitude = df.Range_m  # Using range as altitude for vertical pointing
    
    noise_level = estimate_noise_level(photon_counts, altitude)
    corrected_counts = apply_noise_correction(photon_counts, noise_level)
    
    # Add corrected data to DataFrame
    df.Noise_Level = fill(noise_level, nrow(df))
    df.Corrected_Counts = corrected_counts
    
    mkpath(dirname(output_csv))
    CSV.write(output_csv, df)
    println("✅ Noise corrected data saved: $output_csv")
    
    return output_csv
end

"""
    find_csv_files_recursive(directory::String) -> Vector{String}

Recursively find all CSV files in directory and subdirectories, excluding already processed files.
"""
function find_csv_files_recursive(directory::String)
    if !isdir(directory)
        return String[]
    end

    csv_files = String[]

    # Check current directory
    for item in readdir(directory, join=true)
        if isfile(item) && endswith(item, ".csv") && !contains(item, "noise_corrected") && !contains(item, "temp.dat")
            push!(csv_files, item)
        elseif isdir(item)
            # Recursively search subdirectories
            subdirectory_files = find_csv_files_recursive(item)
            append!(csv_files, subdirectory_files)
        end
    end

    return sort(csv_files)
end

"""
    batch_process_noise_correction(input_dir::String, output_dir::String="data") -> Vector{String}

Batch process noise correction for all CSV files, including those in subdirectories.
"""
function batch_process_noise_correction(input_dir::String, output_dir::String="data")
    println("🔄 Batch processing noise correction...")

    # Use recursive search to find all CSV files
    csv_files = find_csv_files_recursive(input_dir)

    if isempty(csv_files)
        println("⚠️  No CSV files found in $input_dir or its subdirectories")
        return String[]
    end

    println("📁 Found $(length(csv_files)) CSV files (including subdirectories)")

    processed_files = String[]

    for csv_file in csv_files
        try
            output_csv = joinpath(output_dir, replace(basename(csv_file), ".csv" => "_noise_corrected.csv"))
            result_file = process_noise_correction_csv(csv_file, output_csv)
            push!(processed_files, result_file)
        catch e
            println("❌ Error processing $(basename(csv_file)): $e")
        end
    end

    println("📊 Processed $(length(processed_files)) files")
    return processed_files
end

# Export functions
export estimate_noise_level, apply_noise_correction
export process_noise_correction_csv, batch_process_noise_correction, find_csv_files_recursive
