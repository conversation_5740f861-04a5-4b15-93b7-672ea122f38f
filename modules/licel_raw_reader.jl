# LICEL Raw Data Reader Module - Simplified Version
# Matches pre-processing codes exactly

using CSV
using DataFrames
using Dates
using Statistics

# Simple data structure
struct LidarData
    filename::String
    raw_counts::Vector{UInt32}
    range::Vector{Float64}
end

"""
    read_licel_raw_file(filepath::String) -> LidarData

Read LICEL raw file exactly like pre-processing code.
"""
function read_licel_raw_file(filepath::String)
    if !isfile(filepath)
        error("File not found: $filepath")
    end
    
    raw_counts = UInt32[]
    
    open(filepath, "r") do io
        # Skip 3 header lines (matching pre-processing code)
        for _ in 1:3
            if !eof(io)
                readline(io)
            end
        end

        # Read binary UInt32 data
        while !eof(io)
            try
                push!(raw_counts, read(io, UInt32))
            catch
                break
            end
        end
    end
    
    # Calculate range: range = 15 + (i-1) * 30 (matching RTI_Plotting)
    range = [15.0 + (i-1) * 30.0 for i in 1:length(raw_counts)]
    
    return LidarData(basename(filepath), raw_counts, range)
end

"""
    save_lidar_csv(data::LidarData, output_path::String)

Save LIDAR data as simple CSV.
"""
function save_lidar_csv(data::LidarData, output_path::String)
    df = DataFrame(
        Index = 1:length(data.raw_counts),
        Counts = data.raw_counts,
        Range_m = data.range
    )

    mkpath(dirname(output_path))
    CSV.write(output_path, df)
    println("✅ CSV saved: $output_path")
end

"""
    check_overflow_values(raw_counts::Vector{UInt32}) -> Dict{String, Any}

Check for overflow values in UInt32 raw counts data.
Returns analysis of potential overflow conditions.
"""
function check_overflow_values(raw_counts::Vector{UInt32})
    max_uint32 = typemax(UInt32)
    max_int32 = typemax(Int32)

    # Count values at or near overflow
    overflow_count = sum(raw_counts .== max_uint32)
    near_overflow_count = sum(raw_counts .> max_int32)

    analysis = Dict{String, Any}(
        "total_points" => length(raw_counts),
        "max_value" => maximum(raw_counts),
        "overflow_count" => overflow_count,
        "near_overflow_count" => near_overflow_count,
        "overflow_percentage" => (overflow_count / length(raw_counts)) * 100,
        "has_overflow" => overflow_count > 0
    )

    if analysis["has_overflow"]
        println("⚠️  Overflow detected: $(overflow_count) points at UInt32 maximum")
    end

    if near_overflow_count > 0
        println("📊 Near-overflow: $(near_overflow_count) points > Int32 maximum")
    end

    return analysis
end

"""
    find_licel_files(directory::String) -> Vector{String}

Find LICEL files in directory and subdirectories recursively.
Excludes temp.dat files as specified in requirements.
"""
function find_licel_files(directory::String)
    if !isdir(directory)
        return String[]
    end

    files = String[]

    # Check current directory
    for item in readdir(directory, join=true)
        if isfile(item)
            filename = basename(item)
            # Check if it's a LICEL file (starts with I, has one dot, not temp.dat)
            if startswith(filename, "I") && length(split(filename, ".")) == 2 && filename != "temp.dat"
                push!(files, item)
            end
        elseif isdir(item)
            # Recursively search subdirectories
            subdirectory_files = find_licel_files(item)
            append!(files, subdirectory_files)
        end
    end

    return sort(files)
end

"""
    process_clear_sky_folder(input_dir::String, output_dir::String="data") -> Vector{String}

Process all LICEL files in folder and subdirectories recursively.
"""
function process_clear_sky_folder(input_dir::String, output_dir::String="data")
    println("Processing folder: $input_dir")

    licel_files = find_licel_files(input_dir)

    if isempty(licel_files)
        println("⚠️  No LICEL files found in $input_dir or its subdirectories")
        return String[]
    end

    println("📁 Found $(length(licel_files)) LICEL files (including subdirectories)")

    # Show breakdown by subdirectory for debugging
    subdirs = Dict{String, Int}()
    for file_path in licel_files
        subdir = dirname(file_path)
        subdirs[subdir] = get(subdirs, subdir, 0) + 1
    end

    for (subdir, count) in subdirs
        relative_path = replace(subdir, input_dir => ".")
        println("  📂 $relative_path: $count files")
    end

    processed_files = String[]

    for (i, file_path) in enumerate(licel_files)
        try
            println("Processing file $i/$(length(licel_files)): $(basename(file_path))")

            data = read_licel_raw_file(file_path)

            folder_name = basename(input_dir)
            base_name = basename(file_path)
            output_filename = "$(folder_name)_$(base_name).csv"
            output_path = joinpath(output_dir, output_filename)

            save_lidar_csv(data, output_path)
            push!(processed_files, output_path)

        catch e
            println("❌ Error processing $(basename(file_path)): $e")
        end
    end

    println("📊 Processed $(length(processed_files)) files")
    return processed_files
end

"""
    batch_process_clear_sky_data() -> Vector{String}

Process all clear sky folders.
"""
function batch_process_clear_sky_data()
    println("="^60)
    println("BATCH PROCESSING CLEAR SKY CBL DATA")
    println("="^60)

    clear_sky_folders = [
        "clear sky CBL Aratrika/08May2023_CBL_clearsky",
        "clear sky CBL Aratrika/10May2023_CBL_clear Sky",
        "clear sky CBL Aratrika/16May2023_CBL_study_clear_sky",
        "clear sky CBL Aratrika/18May2023_CBL_study_clearsky"
    ]

    all_processed_files = String[]

    for folder in clear_sky_folders
        if isdir(folder)
            println("\n" * "="^40)
            processed_files = process_clear_sky_folder(folder, "data")
            append!(all_processed_files, processed_files)
        else
            println("⚠️  Folder not found: $folder")
        end
    end

    println("\n" * "="^60)
    println("BATCH PROCESSING COMPLETE")
    println("Total files processed: $(length(all_processed_files))")
    println("="^60)

    return all_processed_files
end

# Export functions
export LidarData
export read_licel_raw_file, save_lidar_csv, check_overflow_values
export find_licel_files, process_clear_sky_folder, batch_process_clear_sky_data
