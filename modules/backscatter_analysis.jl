# Backscatter Analysis Module - Simplified Version

using CSV
using DataFrames
using Statistics

"""
    calculate_total_backscatter(normalized::Vector{Float64}, beta_molecular::Vector{Float64}) -> Vector{Float64}

Calculate total backscatter coefficient: βₜₒₜₐₗ(z) ∝ Xⁿ²(z)
The proportionality constant is determined by calibration to molecular backscatter.
Uses improved scaling to preserve numerical precision.
"""
function calculate_total_backscatter(normalized::Vector{Float64}, beta_molecular::Vector{Float64})
    # βₜₒₜₐₗ(z) ∝ Xⁿ²(z)
    # Calibrate by assuming normalized signal equals molecular backscatter in clean regions
    # where Xⁿ²(z) ≈ 1 and βₜₒₜₐₗ ≈ βₘ

    # Find clean regions where normalized signal is close to reference level
    # Use broader range to find more calibration points
    clean_indices = findall(x -> 0.5 <= x <= 2.0, normalized)

    if !isempty(clean_indices)
        # Calibration: βₜₒₜₐₗ = Xⁿ² × βₘ in clean regions
        # Use median for more robust calibration
        calibration_factor = median(beta_molecular[clean_indices])
        beta_total = normalized .* calibration_factor

        println("📊 Calibration: Found $(length(clean_indices)) clean points, factor = $(calibration_factor)")
    else
        # Fallback: use molecular backscatter as calibration
        calibration_factor = mean(beta_molecular)
        beta_total = normalized .* calibration_factor

        println("⚠️  No clean regions found, using mean molecular backscatter: $(calibration_factor)")
    end

    return beta_total
end

"""
    calculate_aerosol_backscatter(beta_total::Vector{Float64}, beta_molecular::Vector{Float64}) -> Vector{Float64}

Calculate aerosol backscatter coefficient: βₐ(z) = βₜₒₜₐₗ(z) − βₘ(z)
Includes improved handling of small values and debugging information.
"""
function calculate_aerosol_backscatter(beta_total::Vector{Float64}, beta_molecular::Vector{Float64})
    # βₐ(z) = βₜₒₜₐₗ(z) − βₘ(z)
    beta_aerosol = beta_total .- beta_molecular

    # Count negative values before clipping
    negative_count = sum(beta_aerosol .< 0)
    if negative_count > 0
        println("⚠️  Found $negative_count negative aerosol values (will be set to zero)")
    end

    # Ensure non-negative (physical constraint)
    beta_aerosol = max.(beta_aerosol, 0.0)

    # Report statistics
    non_zero_count = sum(beta_aerosol .> 0)
    max_aerosol = maximum(beta_aerosol)
    mean_aerosol = mean(beta_aerosol[beta_aerosol .> 0])

    println("📊 Aerosol backscatter stats:")
    println("   Non-zero values: $non_zero_count/$(length(beta_aerosol))")
    println("   Maximum: $(max_aerosol)")
    if non_zero_count > 0
        println("   Mean (non-zero): $(mean_aerosol)")
    end

    return beta_aerosol
end

"""
    process_backscatter_analysis_csv(input_csv::String, output_csv::String="") -> String

Process backscatter analysis for CSV file.
"""
function process_backscatter_analysis_csv(input_csv::String, output_csv::String="")
    if isempty(output_csv)
        output_csv = replace(input_csv, ".csv" => "_backscatter.csv")
    end
    
    println("Processing backscatter analysis: $(basename(input_csv))")
    
    df = CSV.read(input_csv, DataFrame, comment="#")
    
    # Check required columns
    if !("Normalized" in names(df)) || !("Beta_molecular" in names(df))
        error("Required columns not found. Run previous processing steps first.")
    end
    
    normalized = df.Normalized
    beta_molecular = df.Beta_molecular
    
    beta_total = calculate_total_backscatter(normalized, beta_molecular)
    beta_aerosol = calculate_aerosol_backscatter(beta_total, beta_molecular)
    
    # Add backscatter data
    df.Beta_total = beta_total
    df.Beta_aerosol = beta_aerosol
    
    mkpath(dirname(output_csv))
    CSV.write(output_csv, df)
    println("✅ Backscatter analysis data saved: $output_csv")
    
    return output_csv
end

"""
    batch_process_backscatter_analysis(input_dir::String, output_dir::String="data") -> Vector{String}

Batch process backscatter analysis.
"""
function batch_process_backscatter_analysis(input_dir::String, output_dir::String="data")
    println("🔄 Batch processing backscatter analysis...")
    
    csv_files = filter(file -> endswith(file, "_molecular.csv"),
                      readdir(input_dir, join=true))
    
    if isempty(csv_files)
        println("⚠️  No molecular CSV files found")
        return String[]
    end
    
    processed_files = String[]
    
    for csv_file in csv_files
        try
            output_csv = joinpath(output_dir, replace(basename(csv_file), "_molecular.csv" => "_backscatter.csv"))
            result_file = process_backscatter_analysis_csv(csv_file, output_csv)
            push!(processed_files, result_file)
        catch e
            println("❌ Error processing $(basename(csv_file)): $e")
        end
    end
    
    println("📊 Processed $(length(processed_files)) files")
    return processed_files
end

# Export functions
export calculate_total_backscatter, calculate_aerosol_backscatter
export process_backscatter_analysis_csv, batch_process_backscatter_analysis
