# Range Correction Module - Simplified Version

using CSV
using DataFrames

"""
    apply_range_correction(corrected_counts::Vector{Float64}, range::Vector{Float64}) -> Vector{Float64}

Apply range correction: X²(z) = S_clean(z) × z²
LIDAR signal weakens as 1/z². Multiply by z² to compensate.
z = range in meters; bin centers: 15 m, 45 m, etc.
"""
function apply_range_correction(corrected_counts::Vector{Float64}, range::Vector{Float64})
    # X²(z) = S_clean(z) × z²
    range_corrected = corrected_counts .* (range .^ 2)
    return range_corrected
end

"""
    process_range_correction_csv(input_csv::String, output_csv::String="") -> String

Process range correction for CSV file.
"""
function process_range_correction_csv(input_csv::String, output_csv::String="")
    if isempty(output_csv)
        output_csv = replace(input_csv, ".csv" => "_range_corrected.csv")
    end
    
    println("Processing range correction: $(basename(input_csv))")
    
    df = CSV.read(input_csv, DataFrame, comment="#")
    
    # Check required columns
    if !("Corrected_Counts" in names(df)) || !("Range_m" in names(df))
        error("Required columns not found. Run noise correction first.")
    end
    
    corrected_counts = df.Corrected_Counts
    range = df.Range_m
    
    range_corrected = apply_range_correction(corrected_counts, range)
    
    # Add range corrected data
    df.Range_Corrected = range_corrected
    
    mkpath(dirname(output_csv))
    CSV.write(output_csv, df)
    println("✅ Range corrected data saved: $output_csv")
    
    return output_csv
end

"""
    batch_process_range_correction(input_dir::String, output_dir::String="data") -> Vector{String}

Batch process range correction.
"""
function batch_process_range_correction(input_dir::String, output_dir::String="data")
    println("🔄 Batch processing range correction...")
    
    csv_files = filter(file -> endswith(file, "_noise_corrected.csv"),
                      readdir(input_dir, join=true))
    
    if isempty(csv_files)
        println("⚠️  No noise corrected CSV files found")
        return String[]
    end
    
    processed_files = String[]
    
    for csv_file in csv_files
        try
            output_csv = joinpath(output_dir, replace(basename(csv_file), "_noise_corrected.csv" => "_range_corrected.csv"))
            result_file = process_range_correction_csv(csv_file, output_csv)
            push!(processed_files, result_file)
        catch e
            println("❌ Error processing $(basename(csv_file)): $e")
        end
    end
    
    println("📊 Processed $(length(processed_files)) files")
    return processed_files
end

# Export functions
export apply_range_correction
export process_range_correction_csv, batch_process_range_correction
