# Molecular Backscatter Module - Simplified Version

using CSV
using DataFrames

"""
    calculate_molecular_backscatter(altitude::Vector{Float64}) -> Vector{Float64}

Calculate molecular backscatter using standard atmosphere.
Uses appropriate scaling for LIDAR applications.
"""
function calculate_molecular_backscatter(altitude::Vector{Float64})
    # Molecular backscatter calculation with proper scaling
    # Using exponential decay with scale height
    scale_height = 8000.0  # meters

    # Use realistic molecular backscatter coefficient for 532nm
    # Typical values: 1-3 × 10^-6 m^-1 sr^-1 at sea level for molecular scattering
    sea_level_beta = 1.5e-6  # m⁻¹ sr⁻¹ (realistic atmospheric value)

    # Calculate molecular backscatter with altitude dependence
    beta_molecular = sea_level_beta .* exp.(-altitude ./ scale_height)

    return beta_molecular
end

"""
    process_molecular_backscatter_csv(input_csv::String, output_csv::String="") -> String

Process molecular backscatter for CSV file.
"""
function process_molecular_backscatter_csv(input_csv::String, output_csv::String="")
    if isempty(output_csv)
        output_csv = replace(input_csv, ".csv" => "_molecular.csv")
    end
    
    println("Processing molecular backscatter: $(basename(input_csv))")
    
    df = CSV.read(input_csv, DataFrame, comment="#")
    
    # Check required columns
    if !("Range_m" in names(df))
        error("Required column 'Range_m' not found")
    end
    
    altitude = df.Range_m
    beta_molecular = calculate_molecular_backscatter(altitude)
    
    # Add molecular backscatter data
    df.Beta_molecular = beta_molecular
    
    mkpath(dirname(output_csv))
    CSV.write(output_csv, df)
    println("✅ Molecular backscatter data saved: $output_csv")
    
    return output_csv
end

"""
    batch_process_molecular_backscatter(input_dir::String, output_dir::String="data") -> Vector{String}

Batch process molecular backscatter.
"""
function batch_process_molecular_backscatter(input_dir::String, output_dir::String="data")
    println("🔄 Batch processing molecular backscatter...")
    
    csv_files = filter(file -> endswith(file, "_normalized.csv"),
                      readdir(input_dir, join=true))
    
    if isempty(csv_files)
        println("⚠️  No normalized CSV files found")
        return String[]
    end
    
    processed_files = String[]
    
    for csv_file in csv_files
        try
            output_csv = joinpath(output_dir, replace(basename(csv_file), "_normalized.csv" => "_molecular.csv"))
            result_file = process_molecular_backscatter_csv(csv_file, output_csv)
            push!(processed_files, result_file)
        catch e
            println("❌ Error processing $(basename(csv_file)): $e")
        end
    end
    
    println("📊 Processed $(length(processed_files)) files")
    return processed_files
end

# Export functions
export calculate_molecular_backscatter
export process_molecular_backscatter_csv, batch_process_molecular_backscatter
