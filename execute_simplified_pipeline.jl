# Simplified LIDAR Processing Pipeline Execution
# Clean design without unnecessary plotting complications

using Printf
using Dates
using Statistics
using CSV
using DataFrames

# Include core modules directly
include("modules/licel_raw_reader.jl")
include("modules/noise_correction.jl")
include("modules/range_correction.jl")
include("modules/normalization.jl")
include("modules/molecular_backscatter.jl")
include("modules/backscatter_analysis.jl")
include("modules/asr_analysis.jl")
include("modules/aod_calculation.jl")

println("="^80)
println("SIMPLIFIED LIDAR PROCESSING PIPELINE - RESEARCH PUBLICATION")
println("Clean Design - May 2023 Clear Sky CBL Data")
println("="^80)
println("Start Time: $(now())")
println("="^80)

# Processing statistics
processing_stats = Dict{String, Any}()
total_files_processed = 0

"""
Process single file through complete pipeline (simplified)
"""
function process_file_simplified(file_path::String, output_dir::String)
    base_name = basename(file_path)
    
    try
        # Step 1: Read raw LICEL file
        data = read_licel_raw_file(file_path)
        csv_path = joinpath(output_dir, "$(base_name).csv")
        save_lidar_csv(data, csv_path)
        
        # Step 2: Noise correction
        noise_csv = process_noise_correction_csv(csv_path)
        
        # Step 3: Range correction
        range_csv = process_range_correction_csv(noise_csv)
        
        # Step 4: Normalization
        norm_csv = process_normalization_csv(range_csv)
        
        # Step 5: Molecular backscatter
        mol_csv = process_molecular_backscatter_csv(norm_csv)
        
        # Step 6: Backscatter analysis
        back_csv = process_backscatter_analysis_csv(mol_csv)
        
        # Step 7: ASR analysis
        asr_csv = process_asr_analysis_csv(back_csv)
        
        # Step 8: AOD calculation
        aod_csv = process_aod_calculation_csv(asr_csv)
        
        return aod_csv
        
    catch e
        println("❌ Error processing $(base_name): $e")
        return nothing
    end
end

"""
Execute complete processing for all dates
"""
function execute_complete_processing()
    # Processing targets
    processing_targets = [
        ("2023-05-08", "clear sky CBL Aratrika/08May2023_CBL_clearsky"),
        ("2023-05-10", "clear sky CBL Aratrika/10May2023_CBL_clear Sky"),
        ("2023-05-16", "clear sky CBL Aratrika/16May2023_CBL_study_clear_sky"),
        ("2023-05-18", "clear sky CBL Aratrika/18May2023_CBL_study_clearsky")
    ]
    
    global total_files_processed
    
    for (date_str, source_dir) in processing_targets
        println("\n" * "="^50)
        println("PROCESSING DATE: $date_str")
        println("="^50)
        
        start_time = time()
        
        # Create output directory
        output_dir = joinpath("data", date_str)
        mkpath(output_dir)
        
        # Find all LICEL files
        licel_files = find_licel_files(source_dir)
        date_files_count = length(licel_files)
        
        println("📁 Found $date_files_count LICEL files")
        println("📂 Source: $source_dir")
        println("📂 Output: $output_dir")
        
        if isempty(licel_files)
            println("⚠️  No files found for $date_str")
            continue
        end
        
        # Process each file
        successful_files = 0
        failed_files = 0
        
        for (i, file_path) in enumerate(licel_files)
            result = process_file_simplified(file_path, output_dir)
            
            if result !== nothing
                successful_files += 1
                total_files_processed += 1
            else
                failed_files += 1
            end
            
            # Progress indicator
            if i % 25 == 0 || i == date_files_count
                progress = round(i/date_files_count*100, digits=1)
                println("   📊 Progress: $progress% ($i/$date_files_count) - Success: $successful_files, Failed: $failed_files")
            end
        end
        
        # Date processing summary
        date_time = time() - start_time
        
        processing_stats["$(date_str)_processed"] = successful_files
        processing_stats["$(date_str)_failed"] = failed_files
        processing_stats["$(date_str)_time"] = date_time
        
        println("\n📊 $date_str SUMMARY:")
        println("   ✅ Successful: $successful_files")
        println("   ❌ Failed: $failed_files")
        println("   ⏱️  Time: $(round(date_time/60, digits=1)) minutes")
        println("   📈 Success Rate: $(round(successful_files/date_files_count*100, digits=1))%")
    end
end

"""
Quality check on processed data
"""
function quality_check()
    println("\n🔍 QUALITY ASSURANCE CHECK")
    println("="^60)
    
    target_dates = ["2023-05-08", "2023-05-10", "2023-05-16", "2023-05-18"]
    
    for date_str in target_dates
        data_dir = joinpath("data", date_str)
        
        if !isdir(data_dir)
            println("❌ $date_str: No data directory")
            continue
        end
        
        # Count AOD files (final stage)
        aod_files = filter(f -> endswith(f, "_aod.csv"), readdir(data_dir))
        println("✅ $date_str: $(length(aod_files)) AOD files")
        
        # Sample quality check
        if !isempty(aod_files)
            sample_file = joinpath(data_dir, aod_files[1])
            df = CSV.read(sample_file, DataFrame)
            
            if "ASR" in names(df) && "AOD_total" in names(df)
                asr_values = df.ASR
                aod_total = df.AOD_total[1]
                clean_air_pct = sum(asr_values .== 1.0) / length(asr_values) * 100
                
                println("   📊 Sample ASR: $(round(mean(asr_values), digits=3)) ($(round(clean_air_pct, digits=1))% clean air)")
                println("   📊 Sample AOD: $(round(aod_total, digits=4))")
            end
        end
    end
end

"""
Generate final report
"""
function generate_final_report()
    println("\n📊 FINAL PROCESSING REPORT")
    println("="^60)
    
    total_expected = sum(get(processing_stats, "$(date)_processed", 0) + get(processing_stats, "$(date)_failed", 0) 
                        for date in ["2023-05-08", "2023-05-10", "2023-05-16", "2023-05-18"])
    
    println("📈 PROCESSING STATISTICS:")
    println("   Total Files Processed: $total_files_processed")
    println("   Total Files Expected: $total_expected")
    println("   Overall Success Rate: $(round(total_files_processed/total_expected*100, digits=1))%")
    
    # Create summary report
    report_content = """
# LIDAR Processing Report - Simplified Pipeline

**Processing Date:** $(now())
**Dataset:** May 2023 Clear Sky CBL Data
**Pipeline:** Simplified design without plotting complications
**Total Files Processed:** $total_files_processed

## Results by Date

"""
    
    for date_str in ["2023-05-08", "2023-05-10", "2023-05-16", "2023-05-18"]
        if haskey(processing_stats, "$(date_str)_processed")
            processed = processing_stats["$(date_str)_processed"]
            failed = processing_stats["$(date_str)_failed"]
            time_min = round(processing_stats["$(date_str)_time"]/60, digits=1)
            success_rate = round(processed/(processed+failed)*100, digits=1)
            
            report_content *= """
### $date_str
- **Processed:** $processed files
- **Failed:** $failed files
- **Success Rate:** $success_rate%
- **Time:** $time_min minutes

"""
        end
    end
    
    report_content *= """
## Pipeline Design

**Simplified Architecture:**
- ✅ Removed unnecessary plotting arguments
- ✅ Direct module-to-module processing
- ✅ Clean function signatures
- ✅ Focus on core scientific processing

**Output Structure:**
```
data/
├── 2023-05-08/    # May 8 processed files
├── 2023-05-10/    # May 10 processed files
├── 2023-05-16/    # May 16 processed files
└── 2023-05-18/    # May 18 processed files
```

## Research Publication Ready
Complete dataset with realistic clear sky CBL signatures.
"""
    
    open("LIDAR_Simplified_Processing_Report.md", "w") do f
        write(f, report_content)
    end
    
    println("   ✅ Report saved: LIDAR_Simplified_Processing_Report.md")
end

# Execute simplified pipeline
println("🚀 Starting simplified pipeline execution...")

execute_complete_processing()
quality_check()
generate_final_report()

println("\n" * "="^80)
println("SIMPLIFIED PIPELINE EXECUTION COMPLETE")
println("End Time: $(now())")
println("="^80)
println("🎯 CLEAN RESEARCH DATASET READY")
println("="^80)
